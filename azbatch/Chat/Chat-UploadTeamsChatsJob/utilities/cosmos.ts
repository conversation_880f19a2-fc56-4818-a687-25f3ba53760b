import { Con<PERSON><PERSON>, CosmosClient } from "@azure/cosmos";
import { IBatchResponseData, ITeamsChatsMessages } from "./models";
import { CustomLogger } from "./log";
import { updateTableStorageChatsStatus } from "./tableStorage";

import * as dotenv from "dotenv";

dotenv.config();

const cosmosDBEndpoint = process.env["COSMOS_DB_ENDPOINT"];
const cosmosDBKey = process.env["COSMOS_DB_KEY"];
const databaseName = process.env["COSMOS_DB_DATABASE"];
const containerName = process.env["COSMOS_DB_CONTAINER"];

let cosmosClient: CosmosClient | null = null;

function getClient(logger: CustomLogger) {
  try {
    if (!cosmosDBEndpoint || !cosmosDBKey) {
      throw new Error("[CosmosDB:getClient] cosmosDBEndpoint and cosmosDBKey must be defined");
    }
    if (!cosmosClient) {
      logger.log("[CosmosDB:getClient] Initializing Client Connection...");
      cosmosClient = new CosmosClient({
        endpoint: cosmosDBEndpoint,
        key: cosmosDB<PERSON>ey,
      });
      logger.log("[CosmosDB:getClient] Client Initialized Successfully");
    } else {
      logger.log("[CosmosDB:getClient] Reusing Existing Connection");
    }

    return cosmosClient;
  } catch (error) {
    logger.log(`[CosmosDB:getClient] Error Initialization: ${error}`);
    throw error;
  }
}

export async function validateCosmosDBConnection(
  logger: CustomLogger
): Promise<void> {
  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:validateCosmosDBConnection] databaseName and containerName must be defined");
    }
    const client = getClient(logger);

    // Test General Connection
    logger.log(`[CosmosDB:validateCosmosDBConnection] Testing Cosmos DB Connection...`);
    const { resources: databases } = await client.databases
      .readAll()
      .fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Successfully Connected! Found ${databases.length} Databases`);

    // Test Database Connection
    const database = client.database(databaseName);
    const dbResponse = await database.read();
    if (dbResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Database: ${dbResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Database resource is undefined");
    }

    // Test Container Connections
    const container = database.container(containerName);
    const containerResponse = await container.read();
    if (containerResponse.resource) {
      logger.log(`[CosmosDB:validateCosmosDBConnection] Connected to Container: ${containerResponse.resource.id}`);
    } else {
      throw new Error("[CosmosDB:validateCosmosDBConnection] Container resource is undefined");
    }

    // Count Container Details
    const {
      resources: [count],
    } = await container.items.query("SELECT VALUE COUNT(1) FROM c").fetchAll();
    logger.log(`[CosmosDB:validateCosmosDBConnection] Container has: ${count} Items`);
  } catch (error) {
    logger.log(`[CosmosDB:validateCosmosDBConnection] Error Connection: ${error}`);
    throw error;
  }
}

export async function insertChatsMessages(
  logger: CustomLogger,
  dataChatsMessages: IBatchResponseData[]
): Promise<void> {
  
  logger.log(`[CosmosDB:insertChatsMembers] Total dataChatsMessages to Process: ${dataChatsMessages.length}`);
  // logger.log(`[CosmosDB:insertChatsMessages] dataChatsMessages: ${JSON.stringify(dataChatsMessages)}`); // !!!

  try {
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertChatsMessages] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);

    const modifiedDataChatsMessages: ITeamsChatsMessages[][] = dataChatsMessages
      .map((chatsMessages) => chatsMessages.body?.value || [])
      .filter(Array.isArray);

    const insertedCount = await processChatsMessages(container, modifiedDataChatsMessages, logger);

    logger.log(`[CosmosDB:insertChatsMessages] Inserted: ${insertedCount} New CHATS_MESSAGES to Cosmos DB`);
    const totalMessageCount = modifiedDataChatsMessages.reduce((count, array) => count + (array ? array.length : 0),0 );
    logger.log(`[CosmosDB:insertChatsMessages] Skipped: ${totalMessageCount - insertedCount} Existing CHATS_MESSAGES`);

  } catch (error) {
    logger.log(`[CosmosDB:insertChatsMessages] Error Processing Messages: ${error}`);
    throw error;
  }
}

async function processChatsMessages(
  container: Container,
  modifiedDataChatsMessages: ITeamsChatsMessages[][],
  logger: CustomLogger
): Promise<number> {
  let insertedCount = 0;

  logger.log(`[CosmosDB:processChatsMessages] Total modifiedDataChatsMessages to Process: ${modifiedDataChatsMessages.length}`);
  // logger.log(`[CosmosDB:processChatsMessages] modifiedDataChatsMessages: ${JSON.stringify(modifiedDataChatsMessages)}`); // !!!

  for (const messageArray of modifiedDataChatsMessages) {
    for (const chatsMessages of messageArray) {
      if (!chatsMessages.id) {
        logger.log(`[CosmosDB:processChatsMessages] Error: Missing ID for chatsMessages: ${JSON.stringify(chatsMessages.id)}`);
        continue;
      }

      const querySpec = {
        query: "SELECT * FROM c WHERE c.id = @id",
        parameters: [{ name: "@id", value: chatsMessages.id }]
      };

      try {
        const { resources: existingMessages } = await container.items.query(querySpec).fetchAll();

        if (existingMessages.length === 0) {
          await container.items.create(chatsMessages);
          insertedCount++;
          // logger.log(`[CosmosDB:processChatsMessages] Created New Message: ${chatsMessages.id}`);

        } else {
          // Check if the message has deletedDateTime not null and softDelete is true
          const existingMessage = existingMessages[0];

          if (existingMessage.deletedDateTime !== null && existingMessage.softDelete === true) { 
            existingMessage.lastModifiedDateTime = chatsMessages.lastModifiedDateTime;
            existingMessage.lastEditedDateTime = chatsMessages.lastEditedDateTime;
            existingMessage.deletedDateTime = chatsMessages.deletedDateTime;
            existingMessage.softDelete = chatsMessages.softDelete;
            existingMessage.body.content = chatsMessages.body?.content || "";

            await container.items.upsert(existingMessage);
            insertedCount++;
            logger.log(`[CosmosDB:processChatsMessages] Message: ${chatsMessages.id} - Restored Message`);

          } else {
            // Message exists but doesn't meet update conditions
            // logger.log(`[CosmosDB:processChatsMessages] Message: ${chatsMessages.id} - Already Exists`);
          }
        }

      } catch (error) {
        logger.log(`[CosmosDB:processChatsMessages] Error Processing Message: ${error}`);
      }
    }
  }
  return insertedCount;
}

export async function insertChatsMembers(
  logger: CustomLogger,
  dataChatMembers: IBatchResponseData[]
): Promise<void> {
  logger.log(`[CosmosDB:insertChatsMembers] Total dataChatMembers to Process: ${dataChatMembers.length}`);
  // logger.log(`[CosmosDB:insertChatsMembers] dataChatMembers: ${JSON.stringify(dataChatMembers)}`); // !!!

  try {
    if (!dataChatMembers || dataChatMembers.length === 0) {
      logger.log(`[CosmosDB:insertChatsMembers] No CHATS_MEMBERS data to process`);
      return;
    }
    
    if (!databaseName || !containerName) {
      throw new Error("[CosmosDB:insertChatsMembers] databaseName and containerName must be defined");
    }

    const client = getClient(logger);
    const container = client.database(databaseName).container(containerName);
    
    const modifiedDataChatsMembers = dataChatMembers
      .filter(chat => chat.id && chat.body?.value)
      .map(chat => ({
        chatId: chat.id,
        members: chat.body?.value || []
      }));
    // logger.log(`[CosmosDB:insertChatsMembers] modifiedDataChatsMembers: ${JSON.stringify(modifiedDataChatsMembers)}`);

    const updatedMessagesCount = await processChatMembers(container, modifiedDataChatsMembers, logger);
    logger.log(`[CosmosDB:insertChatsMembers] Successfully Updated: ${updatedMessagesCount} Messages in CosmosDB`);
  
  } catch (error) {
    logger.log(`[CosmosDB:insertChatsMembers] Error processing CHATS_MEMBERS: ${error}`);
    throw error;
  }
}

async function processChatMembers(
  container: Container,
  modifiedDataChatsMembers: any[],
  logger: CustomLogger
): Promise<number> {
  let totalUpdatedCount = 0;
  
  logger.log(`[CosmosDB:processChatMembers] Total modifiedDataChatsMembers to Process: ${modifiedDataChatsMembers.length}`);

  for (const chatEntry of modifiedDataChatsMembers) {
    const chatId = chatEntry.chatId;
    const members = chatEntry.members;

    if (!members || !Array.isArray(members) || members.length === 0) {
      logger.log(`[CosmosDB:processChatMembers] Skipping chat ${chatId} - No Valid Members`);
      continue;
    }
    
    try {
      // Query Chat Messages
      const querySpec = {
        query: "SELECT * FROM c WHERE c.chatId = @chatId",
        parameters: [{ name: "@chatId", value: chatId }]
      };
      
      const { resources: allMessagesOfChatID } = await container.items.query(querySpec).fetchAll();
      logger.log(`[CosmosDB:processChatMembers] Found ${allMessagesOfChatID.length} Messages for chatId: ${chatId}`);
      // logger.log(`[CosmosDB:processChatMembers] allMessagesOfChatID: ${JSON.stringify(allMessagesOfChatID)}`); // !!!

      if (allMessagesOfChatID.length === 0) {
        logger.log(`[CosmosDB:processChatMembers] No Messages Found for chatId: ${chatId}`);
        continue;
      }
      
      let chatUpdatedCount = 0;
      for (const chatMessages of allMessagesOfChatID) {
        // logger.log(`[CosmosDB:processChatMembers] chatMessages ${JSON.stringify(chatMessages)}`);
        const wasUpdated = await processChatsMessagesMembers(chatMessages, members, container, logger);
        if (wasUpdated) {
          chatUpdatedCount++;
          totalUpdatedCount++;
        }
      }
      logger.log(`[CosmosDB:processChatMembers] Completed: ${chatUpdatedCount} of ${allMessagesOfChatID.length} Members - Messages Updated`);
      allMessagesOfChatID.length = 0;

      // // Update Table Storage - lastCheckedDatetime
      // await updateTableStorageChatsStatus(logger, chatEntry);
      
    } catch (error) {
      logger.log(`[CosmosDB:processChatMembers] Error Processing chatId ${chatId}: ${error}`);
    }
  }
  return totalUpdatedCount;
}

async function processChatsMessagesMembers(
  chatMessages: any, 
  validMembers: any[], 
  container: Container, 
  logger: CustomLogger
): Promise<boolean> {
  const messageLastModifiedDatetime = chatMessages.lastModifiedDateTime;
  if (!messageLastModifiedDatetime) {
    logger.log(`[CosmosDB:processChatsMessagesMembers] Skipping chatMessages: ${chatMessages.id} - Missing lastModifiedDateTime`);
    return false;
  }
  
  const messageDate = new Date(messageLastModifiedDatetime);
  if (isNaN(messageDate.getTime())) {
    logger.log(`[CosmosDB:processChatsMessagesMembers] Skipping chatMessages: ${chatMessages.id} - Invalid Date: ${messageLastModifiedDatetime}`);
    return false;
  }

  // Get existing security_user_id from cosmosDB
  let existingSecurityUserIds = chatMessages.security_user_id || [];
  let addedCount = 0;

  // Compare members visibilityHistoryStartDateTime with chatsMessages lastModifiedDateTime
  if (validMembers && Array.isArray(validMembers) && validMembers.length > 0) {
    const eligibleUserIds = checkUserVisibilityHistory(validMembers, messageDate);
    
    for (const userId of eligibleUserIds) {
      if (!existingSecurityUserIds.includes(userId)) {
        existingSecurityUserIds.push(userId);
        addedCount++;
      }
    }
  }

  if (addedCount === 0) {
    // logger.log(`[CosmosDB:processChatsMessagesMembers] No changes needed for Message: ${chatMessages.id} - No valid members to add`);
    return false;
  }

  try {
    chatMessages.security_user_id = [...new Set(existingSecurityUserIds)];
    await container.items.upsert(chatMessages);
    
    // logger.log(`[CosmosDB:processChatsMessagesMembers] Updated Message: ${chatMessages.id} - Added: ${addedCount} Members, Total: ${chatMessages.security_user_id.length} Members`);
    return true;
    
  } catch (error) {
    logger.log(`[CosmosDB:processChatsMessagesMembers] Failed to Update Message: ${chatMessages.id} - ${error}`);
    return false;
  }
}

function checkUserVisibilityHistory(members: any[], messageDate: Date): string[] {
  return members
    .filter(member => {
      if (!member.userId) return false;
      
      const memberJoinDate = new Date(member.visibleHistoryStartDateTime || "0001-01-01T00:00:00Z");
      if (isNaN(memberJoinDate.getTime())) return false;
      
      return messageDate >= memberJoinDate;
    })
    .map(member => member.userId);
}