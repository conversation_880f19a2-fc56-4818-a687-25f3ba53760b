import { getSharePointAccessToken, createSharePointClient } from '../utilities/sharepoint-auth';
import { logger } from "../utilities/log";
import {
  processListItems
} from "./impl";
import * as dotenv from "dotenv";
import { validateCosmosDBConnection } from "../utilities/cosmos";
import { siteConfig } from '../spo-config';

dotenv.config();

async function main() {
  logger.log("\n========== PHASE 1 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:validateCosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:validateCosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }

  logger.log("\n========== PHASE 2 ==========");
  logger.log("--- Getting Access Token ---");

  const config = {
    clientId: process.env["AzureClientId"] ?? '',
    tenantId: process.env["AzureTenantId"] ?? '',
    // certificatePath: './' + (process.env["CertificateName"] ?? ''), // for local
    certificatePath: '../' + (process.env["CertificateName"] ?? ''), // for azure batch
    certificatePassword: process.env["CertificatePassword"] ?? '',
    sharepointHostname: process.env["SharepointHostname"] ?? '',
  };

  // Getting Access Token
  const result = await getSharePointAccessToken(logger, config, false);
  
  if (result.success) {
    logger.log('Token:', result.accessToken);
    logger.log('Expires At:', result.expiresAt);
  } else {
    logger.error('Error:', result.error);
  }

  logger.log("\n========== PHASE 3 ==========");
  logger.log("--- Validating SharePoint Client ---");

  // Test Access Token
  const client = await createSharePointClient(logger, config, false);
  
  // Get Site Info
  const siteResponse = await client.makeRequest('/_api/site');
  const siteData = await siteResponse.json();
  console.log('Site Info:', siteData);

  logger.log("\n========== PHASE 4 ==========");
  logger.log("--- Processing Site List Items ---");
  
  let spoToken = result.accessToken ?? "";

  const siteListsData = siteConfig;
  await processListItems(logger, spoToken, siteListsData)
    .catch((error) => {
      logger.error(`[Index:processListItems] Error: ${error}`);
    });
  logger.log(`[Index:processListItems] Successfully Inserted ListItems.`);

  logger.log("\n========== PHASE 5 ==========");
  logger.log("--- Finish ---");
  logger.log("[Index] All Phase Successfully Completed.");
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});