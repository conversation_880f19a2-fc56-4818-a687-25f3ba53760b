import { execSync } from "child_process";
import path from "path";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

// Directories based on structure
const ROOT_DIR = path.resolve(__dirname, "../");
const JOB_NAME = process.env.SPO_BATCH_JOB_NAME ?? 'default';
const DIST_DIR = path.join(ROOT_DIR, "dist");
const ZIP_FILE = path.join(ROOT_DIR, `${JOB_NAME}.zip`);

function runCommand(command: string): void {
  console.log(`Running: ${command}`);
  execSync(command, { stdio: "inherit" });
}

function ensureDir(dir: string): void {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

function copyFile(src: string, dest: string): void {
  console.log(`Attempting to copy: ${src} -> ${dest}`);
  
  if (!fs.existsSync(src)) {
    console.error(`❌ Source file does not exist: ${src}`);
    return;
  }
  
  ensureDir(path.dirname(dest));
  fs.copyFileSync(src, dest);
  
  if (fs.existsSync(dest)) {
    console.log(`✅ Successfully copied: ${path.basename(dest)}`);
  } else {
    console.error(`❌ Failed to copy: ${dest}`);
  }
}

function copyDir(src: string, dest: string, isWindows: boolean): void {
  if (!fs.existsSync(src)) return;
  
  ensureDir(dest);
  
  if (isWindows) {
    runCommand(`xcopy "${src}" "${dest}" /E /I /Y`);
  } else {
    runCommand(`cp -r "${src}/"* "${dest}"`);
  }
}

async function main(): Promise<void> {
  try {
    console.log("Starting build process...");
    const isWindows = process.platform === "win32";

    // Build TypeScript project
    console.log("Building TypeScript project...");
    runCommand("npx tsc");

    // // Copy certificate to dist folder - YDXXQ
    // console.log("Copying certificate to dist folder...");
    // copyFile(
    //   path.join(ROOT_DIR, "atTaneSPOAccessTokeCert.pfx"), 
    //   path.join(DIST_DIR, "atTaneSPOAccessTokeCert.pfx")
    // );

    // // Copy certificate to dist folder - MJK
    // console.log("Copying certificate to dist folder...");
    // copyFile(
    //   path.join(ROOT_DIR, "atTaneDevSPOAccessTokeCert.pfx"), 
    //   path.join(DIST_DIR, "atTaneDevSPOAccessTokeCert.pfx")
    // );

    // Copy certificate to dist folder - PROD
    console.log("Copying certificate to dist folder...");
    copyFile(
      path.join(ROOT_DIR, "atTaneProdSPOAccessTokenCert.pfx"), 
      path.join(DIST_DIR, "atTaneProdSPOAccessTokenCert.pfx")
    );

    // Create temporary directory for zip
    const tempDir = path.join(ROOT_DIR, "temp_zip");
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
    
    // Create the target directory structure
    const tempDistDir = path.join(tempDir, "dist");
    const tempSrcDir = path.join(tempDistDir, "src");
    const tempUtilDir = path.join(tempDistDir, "utilities");

    ensureDir(tempSrcDir);
    ensureDir(tempUtilDir);

    // Copy compiled src files
    console.log("Copying src files...");
    copyDir(path.join(DIST_DIR, "src"), tempSrcDir, isWindows);
    
    // Copy compiled utilities
    console.log("Copying utilities...");
    copyDir(path.join(DIST_DIR, "utilities"), tempUtilDir, isWindows);
    
    // // Copy atTaneSPOAccessTokeCert.pfx file - YDXXQ
    // console.log("Copying atTaneSPOAccessTokeCert.pfx...");
    // copyFile(
    //   path.join(ROOT_DIR, "atTaneSPOAccessTokeCert.pfx"), 
    //   path.join(tempDistDir, "atTaneSPOAccessTokeCert.pfx")
    // );

    // // Copy atTaneDevSPOAccessTokeCert.pfx file - MJK
    // console.log("Copying atTaneDevSPOAccessTokeCert.pfx...");
    // copyFile(
    //   path.join(ROOT_DIR, "atTaneDevSPOAccessTokeCert.pfx"), 
    //   path.join(tempDistDir, "atTaneDevSPOAccessTokeCert.pfx")
    // );

    // Copy certificate to dist folder - PROD
    console.log("Copying atTaneProdSPOAccessTokenCert.pfx...");
    copyFile(
      path.join(ROOT_DIR, "atTaneProdSPOAccessTokenCert.pfx"), 
      path.join(tempDistDir, "atTaneProdSPOAccessTokenCert.pfx")
    );

    // Copy spo-config.js file
    console.log("Copying spo-config.js...");
    copyFile(
      path.join(DIST_DIR, "spo-config.js"), 
      path.join(tempDistDir, "spo-config.js")
    );

    // Copy package files to root of temp dir
    console.log("Copying package files...");
    copyFile(path.join(ROOT_DIR, "package.json"), path.join(tempDir, "dist", "package.json"));
    copyFile(path.join(ROOT_DIR, "package-lock.json"), path.join(tempDir, "dist", "package-lock.json"));
    copyFile(path.join(ROOT_DIR, "tsconfig.json"), path.join(tempDir, "dist", "tsconfig.json"));

    // Create zip file
    console.log(`Creating ZIP file: ${JOB_NAME}.zip...`);
    if (fs.existsSync(ZIP_FILE)) {
      fs.unlinkSync(ZIP_FILE);
    }
    
    if (isWindows) {
      runCommand(`powershell -command "Compress-Archive -Path '${tempDir}/*' -DestinationPath '${ZIP_FILE}' -Force"`);
    } else {
      runCommand(`cd "${tempDir}" && zip -r "${ZIP_FILE}" .`);
    }

    // Clean up temp directory
    fs.rmSync(tempDir, { recursive: true, force: true });
    console.log("Build process completed successfully!");
  } catch (error) {
    console.error("Error:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

main().catch(error => {
  console.error("Error:", error);
  process.exit(1);
});